# تطبيق إقرأ - نظام إدارة المدارس في المغرب

## نظرة عامة

تطبيق "إقرأ" هو نظام شامل لإدارة المؤسسات التعليمية في المملكة المغربية. يدعم التطبيق الهيكل التنظيمي الهرمي للتعليم في المغرب من المستوى الوطني إلى مستوى المدرسة الفردية.

## الميزات الرئيسية

### الهيكل التنظيمي
- **المستوى الوطني**: مسؤول وطني بصلاحيات كاملة
- **المستوى الجهوي**: مسؤولو الأكاديميات الجهوية
- **المستوى الإقليمي**: مديرو النيابات الإقليمية
- **المستوى المحلي**: مديرو المدارس

### الصلاحيات والأدوار
- **المسؤول الوطني**: إدارة جميع المؤسسات والمستخدمين
- **المسؤول الجهوي**: إدارة النيابات الإقليمية في أكاديميته
- **المدير الإقليمي**: إدارة المدارس في نيابته
- **مدير المدرسة**: إدارة مدرسته فقط

### المنصات المدعومة
- Android
- iOS
- Windows
- Web

## التقنيات المستخدمة

- **Framework**: Flutter
- **State Management**: Riverpod
- **Navigation**: GoRouter
- **Local Storage**: SharedPreferences
- **HTTP Client**: Dio
- **UI Components**: Material Design 3
- **Localization**: Flutter Intl

## بنية المشروع

```
lib/
├── core/
│   ├── constants/          # الثوابت العامة
│   ├── models/            # نماذج البيانات الأساسية
│   ├── providers/         # مزودي الحالة العامة
│   ├── router/           # نظام التوجيه
│   ├── services/         # الخدمات العامة
│   └── theme/            # تصميم التطبيق
├── features/
│   ├── auth/             # المصادقة والتفويض
│   ├── dashboard/        # لوحة التحكم
│   ├── institutions/     # إدارة المؤسسات
│   ├── users/           # إدارة المستخدمين
│   └── profile/         # الملف الشخصي
└── main.dart            # نقطة دخول التطبيق
```

## الحسابات التجريبية

للاختبار، يمكنك استخدام الحسابات التالية:

### المسؤول الوطني
- **البريد الإلكتروني**: <EMAIL>
- **كلمة المرور**: admin123
- **الصلاحيات**: جميع الصلاحيات

### المسؤول الجهوي
- **البريد الإلكتروني**: <EMAIL>
- **كلمة المرور**: regional123
- **الصلاحيات**: إدارة النيابات والمدارس في الأكاديمية

### المدير الإقليمي
- **البريد الإلكتروني**: <EMAIL>
- **كلمة المرور**: provincial123
- **الصلاحيات**: إدارة المدارس في النيابة

### مدير المدرسة
- **البريد الإلكتروني**: <EMAIL>
- **كلمة المرور**: principal123
- **الصلاحيات**: إدارة المدرسة فقط

## التثبيت والتشغيل

### المتطلبات
- Flutter SDK (3.1.0 أو أحدث)
- Dart SDK
- Android Studio / VS Code
- Git

### خطوات التثبيت

1. **استنساخ المشروع**
```bash
git clone <repository-url>
cd إقرأ
```

2. **تثبيت التبعيات**
```bash
flutter pub get
```

3. **تشغيل التطبيق**
```bash
# للويب
flutter run -d chrome

# للأندرويد
flutter run -d android

# للويندوز
flutter run -d windows
```

## البناء للإنتاج

### بناء تطبيق الويب
```bash
flutter build web
```

### بناء تطبيق الأندرويد
```bash
flutter build apk --release
# أو
flutter build appbundle --release
```

### بناء تطبيق الويندوز
```bash
flutter build windows --release
```

## المساهمة

نرحب بالمساهمات! يرجى اتباع الخطوات التالية:

1. Fork المشروع
2. إنشاء فرع للميزة الجديدة (`git checkout -b feature/amazing-feature`)
3. Commit التغييرات (`git commit -m 'Add some amazing feature'`)
4. Push للفرع (`git push origin feature/amazing-feature`)
5. فتح Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## الدعم

للحصول على الدعم أو الإبلاغ عن مشاكل، يرجى فتح issue في GitHub أو التواصل معنا عبر:

- البريد الإلكتروني: <EMAIL>
- الهاتف: +212 5XX XXX XXX

## خارطة الطريق

### المرحلة الحالية (v1.0)
- [x] نظام المصادقة والتفويض
- [x] لوحة التحكم الأساسية
- [x] إدارة المستخدمين الأساسية
- [ ] إدارة المؤسسات التعليمية
- [ ] التقارير والإحصائيات

### المراحل القادمة
- [ ] نظام الإشعارات
- [ ] إدارة الطلاب والمعلمين
- [ ] نظام الحضور والغياب
- [ ] إدارة المناهج والجداول
- [ ] تطبيق الهاتف المحمول
- [ ] API للتكامل مع الأنظمة الأخرى

---

**تم تطوير هذا التطبيق بواسطة فريق إقرأ لخدمة التعليم في المملكة المغربية** 🇲🇦

class AppConstants {
  // App Info
  static const String appName = 'إقرأ';
  static const String appVersion = '1.0.0';
  static const String appDescription = 'نظام إدارة المدارس في المغرب';
  
  // API
  static const String baseUrl = 'https://api.iqraa.ma';
  static const String apiVersion = 'v1';
  
  // Storage Keys
  static const String userTokenKey = 'user_token';
  static const String userDataKey = 'user_data';
  static const String languageKey = 'language';
  static const String themeKey = 'theme';
  
  // User Roles
  static const String nationalAdminRole = 'national_admin';
  static const String regionalManagerRole = 'regional_manager';
  static const String provincialDirectorRole = 'provincial_director';
  static const String schoolPrincipalRole = 'school_principal';
  
  // Administrative Levels
  static const String nationalLevel = 'national';
  static const String regionalLevel = 'regional';
  static const String provincialLevel = 'provincial';
  static const String schoolLevel = 'school';
  
  // Institution Types
  static const String primarySchool = 'primary_school';
  static const String middleSchool = 'middle_school';
  static const String highSchool = 'high_school';
  static const String regionalAcademy = 'regional_academy';
  static const String provincialDirection = 'provincial_direction';
  
  // Moroccan Regions
  static const List<String> moroccanRegions = [
    'طنجة-تطوان-الحسيمة',
    'الشرق',
    'فاس-مكناس',
    'الرباط-سلا-القنيطرة',
    'بني ملال-خنيفرة',
    'الدار البيضاء-سطات',
    'مراكش-آسفي',
    'درعة-تافيلالت',
    'سوس-ماسة',
    'كلميم-واد نون',
    'العيون-الساقية الحمراء',
    'الداخلة-وادي الذهب',
  ];
  
  // UI Constants
  static const double defaultPadding = 16;
  static const double smallPadding = 8;
  static const double largePadding = 24;
  static const double borderRadius = 12;
  static const double cardElevation = 4;
}

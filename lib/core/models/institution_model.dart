import 'package:uuid/uuid.dart';

enum InstitutionType {
  regionalAcademy('regional_academy', 'أكاديمية جهوية'),
  provincialDirection('provincial_direction', 'نيابة إقليمية'),
  primarySchool('primary_school', 'مدرسة ابتدائية'),
  middleSchool('middle_school', 'إعدادية'),
  highSchool('high_school', 'ثانوية');

  const InstitutionType(this.value, this.displayName);
  final String value;
  final String displayName;

  static InstitutionType fromString(String value) => InstitutionType.values.firstWhere(
      (type) => type.value == value,
      orElse: () => InstitutionType.primarySchool,
    );

  bool get isSchool => [primarySchool, middleSchool, highSchool].contains(this);
  bool get isAdministrative => [regionalAcademy, provincialDirection].contains(this);
}

enum InstitutionStatus {
  active('active', 'نشط'),
  inactive('inactive', 'غير نشط'),
  underConstruction('under_construction', 'تحت الإنشاء'),
  maintenance('maintenance', 'تحت الصيانة');

  const InstitutionStatus(this.value, this.displayName);
  final String value;
  final String displayName;

  static InstitutionStatus fromString(String value) => InstitutionStatus.values.firstWhere(
      (status) => status.value == value,
      orElse: () => InstitutionStatus.active,
    );
}

class Address {

  Address({
    required this.street,
    required this.city,
    required this.province,
    required this.region,
    this.postalCode,
    this.latitude,
    this.longitude,
  });

  factory Address.fromJson(Map<String, dynamic> json) {
    return Address(
      street: json['street'],
      city: json['city'],
      province: json['province'],
      region: json['region'],
      postalCode: json['postalCode'],
      latitude: json['latitude']?.toDouble(),
      longitude: json['longitude']?.toDouble(),
    );
  }
  final String street;
  final String city;
  final String province;
  final String region;
  final String? postalCode;
  final double? latitude;
  final double? longitude;

  String get fullAddress => '$street, $city, $province, $region';

  Map<String, dynamic> toJson() => {
      'street': street,
      'city': city,
      'province': province,
      'region': region,
      'postalCode': postalCode,
      'latitude': latitude,
      'longitude': longitude,
    };

  Address copyWith({
    String? street,
    String? city,
    String? province,
    String? region,
    String? postalCode,
    double? latitude,
    double? longitude,
  }) => Address(
      street: street ?? this.street,
      city: city ?? this.city,
      province: province ?? this.province,
      region: region ?? this.region,
      postalCode: postalCode ?? this.postalCode,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
    );
}

class InstitutionModel {

  InstitutionModel({
    String? id,
    required this.name,
    required this.nameArabic,
    this.nameFrench,
    required this.type,
    this.status = InstitutionStatus.active,
    required this.code,
    required this.address,
    this.parentId,
    this.directorId,
    this.phoneNumber,
    this.email,
    this.website,
    this.studentCapacity,
    this.currentStudentCount,
    this.teacherCount,
    this.staffCount,
    required this.establishedDate,
    DateTime? createdAt,
    DateTime? updatedAt,
    this.metadata,
  })  : id = id ?? const Uuid().v4(),
        createdAt = createdAt ?? DateTime.now(),
        updatedAt = updatedAt ?? DateTime.now();

  factory InstitutionModel.fromJson(Map<String, dynamic> json) {
    return InstitutionModel(
      id: json['id'],
      name: json['name'],
      nameArabic: json['nameArabic'],
      nameFrench: json['nameFrench'],
      type: InstitutionType.fromString(json['type']),
      status: InstitutionStatus.fromString(json['status']),
      code: json['code'],
      address: Address.fromJson(json['address']),
      parentId: json['parentId'],
      directorId: json['directorId'],
      phoneNumber: json['phoneNumber'],
      email: json['email'],
      website: json['website'],
      studentCapacity: json['studentCapacity'],
      currentStudentCount: json['currentStudentCount'],
      teacherCount: json['teacherCount'],
      staffCount: json['staffCount'],
      establishedDate: DateTime.parse(json['establishedDate']),
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
      metadata: json['metadata'],
    );
  }
  final String id;
  final String name;
  final String nameArabic;
  final String? nameFrench;
  final InstitutionType type;
  final InstitutionStatus status;
  final String code; // Unique institution code
  final Address address;
  final String? parentId; // Parent institution ID (for hierarchy)
  final String? directorId; // Current director/principal ID
  final String? phoneNumber;
  final String? email;
  final String? website;
  final int? studentCapacity;
  final int? currentStudentCount;
  final int? teacherCount;
  final int? staffCount;
  final DateTime establishedDate;
  final DateTime createdAt;
  final DateTime updatedAt;
  final Map<String, dynamic>? metadata;

  bool get isActive => status == InstitutionStatus.active;
  bool get hasDirector => directorId != null;
  bool get isOverCapacity => 
      studentCapacity != null && 
      currentStudentCount != null && 
      currentStudentCount! > studentCapacity!;

  double? get capacityUtilization {
    if (studentCapacity == null || currentStudentCount == null) return null;
    return (currentStudentCount! / studentCapacity!) * 100;
  }

  InstitutionModel copyWith({
    String? id,
    String? name,
    String? nameArabic,
    String? nameFrench,
    InstitutionType? type,
    InstitutionStatus? status,
    String? code,
    Address? address,
    String? parentId,
    String? directorId,
    String? phoneNumber,
    String? email,
    String? website,
    int? studentCapacity,
    int? currentStudentCount,
    int? teacherCount,
    int? staffCount,
    DateTime? establishedDate,
    DateTime? createdAt,
    DateTime? updatedAt,
    Map<String, dynamic>? metadata,
  }) => InstitutionModel(
      id: id ?? this.id,
      name: name ?? this.name,
      nameArabic: nameArabic ?? this.nameArabic,
      nameFrench: nameFrench ?? this.nameFrench,
      type: type ?? this.type,
      status: status ?? this.status,
      code: code ?? this.code,
      address: address ?? this.address,
      parentId: parentId ?? this.parentId,
      directorId: directorId ?? this.directorId,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      email: email ?? this.email,
      website: website ?? this.website,
      studentCapacity: studentCapacity ?? this.studentCapacity,
      currentStudentCount: currentStudentCount ?? this.currentStudentCount,
      teacherCount: teacherCount ?? this.teacherCount,
      staffCount: staffCount ?? this.staffCount,
      establishedDate: establishedDate ?? this.establishedDate,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      metadata: metadata ?? this.metadata,
    );

  Map<String, dynamic> toJson() => {
      'id': id,
      'name': name,
      'nameArabic': nameArabic,
      'nameFrench': nameFrench,
      'type': type.value,
      'status': status.value,
      'code': code,
      'address': address.toJson(),
      'parentId': parentId,
      'directorId': directorId,
      'phoneNumber': phoneNumber,
      'email': email,
      'website': website,
      'studentCapacity': studentCapacity,
      'currentStudentCount': currentStudentCount,
      'teacherCount': teacherCount,
      'staffCount': staffCount,
      'establishedDate': establishedDate.toIso8601String(),
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'metadata': metadata,
    };

  @override
  String toString() => 'InstitutionModel(id: $id, name: $nameArabic, type: ${type.displayName})';

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is InstitutionModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

import 'package:uuid/uuid.dart';

enum UserRole {
  nationalAdmin('national_admin', 'مسؤول وطني'),
  regionalManager('regional_manager', 'مسؤول جهوي'),
  provincialDirector('provincial_director', 'مدير إقليمي'),
  schoolPrincipal('school_principal', 'مدير مدرسة');

  const UserRole(this.value, this.displayName);
  final String value;
  final String displayName;

  static UserRole fromString(String value) => UserRole.values.firstWhere(
      (role) => role.value == value,
      orElse: () => UserRole.schoolPrincipal,
    );
}

enum UserStatus {
  active('active', 'نشط'),
  inactive('inactive', 'غير نشط'),
  suspended('suspended', 'معلق'),
  pending('pending', 'في الانتظار');

  const UserStatus(this.value, this.displayName);
  final String value;
  final String displayName;

  static UserStatus fromString(String value) => UserStatus.values.firstWhere(
      (status) => status.value == value,
      orElse: () => UserStatus.pending,
    );
}

class UserModel {

  UserModel({
    String? id,
    required this.email,
    required this.firstName,
    required this.lastName,
    this.phoneNumber,
    required this.role,
    this.status = UserStatus.pending,
    this.institutionId,
    this.regionId,
    this.provinceId,
    DateTime? createdAt,
    DateTime? updatedAt,
    this.profileImageUrl,
    this.metadata,
  })  : id = id ?? const Uuid().v4(),
        createdAt = createdAt ?? DateTime.now(),
        updatedAt = updatedAt ?? DateTime.now();

  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      id: json['id'],
      email: json['email'],
      firstName: json['firstName'],
      lastName: json['lastName'],
      phoneNumber: json['phoneNumber'],
      role: UserRole.fromString(json['role']),
      status: UserStatus.fromString(json['status']),
      institutionId: json['institutionId'],
      regionId: json['regionId'],
      provinceId: json['provinceId'],
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
      profileImageUrl: json['profileImageUrl'],
      metadata: json['metadata'],
    );
  }
  final String id;
  final String email;
  final String firstName;
  final String lastName;
  final String? phoneNumber;
  final UserRole role;
  final UserStatus status;
  final String? institutionId; // ID of the institution they manage
  final String? regionId; // For regional managers
  final String? provinceId; // For provincial directors
  final DateTime createdAt;
  final DateTime updatedAt;
  final String? profileImageUrl;
  final Map<String, dynamic>? metadata;

  String get fullName => '$firstName $lastName';

  bool get isNationalAdmin => role == UserRole.nationalAdmin;
  bool get isRegionalManager => role == UserRole.regionalManager;
  bool get isProvincialDirector => role == UserRole.provincialDirector;
  bool get isSchoolPrincipal => role == UserRole.schoolPrincipal;

  bool get isActive => status == UserStatus.active;

  // Permissions based on role
  bool canManageRegions() => isNationalAdmin;
  bool canManageProvinces() => isNationalAdmin || isRegionalManager;
  bool canManageSchools() => isNationalAdmin || isRegionalManager || isProvincialDirector;
  bool canManageUsers() => isNationalAdmin || isRegionalManager || isProvincialDirector;

  UserModel copyWith({
    String? id,
    String? email,
    String? firstName,
    String? lastName,
    String? phoneNumber,
    UserRole? role,
    UserStatus? status,
    String? institutionId,
    String? regionId,
    String? provinceId,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? profileImageUrl,
    Map<String, dynamic>? metadata,
  }) => UserModel(
      id: id ?? this.id,
      email: email ?? this.email,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      role: role ?? this.role,
      status: status ?? this.status,
      institutionId: institutionId ?? this.institutionId,
      regionId: regionId ?? this.regionId,
      provinceId: provinceId ?? this.provinceId,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      profileImageUrl: profileImageUrl ?? this.profileImageUrl,
      metadata: metadata ?? this.metadata,
    );

  Map<String, dynamic> toJson() => {
      'id': id,
      'email': email,
      'firstName': firstName,
      'lastName': lastName,
      'phoneNumber': phoneNumber,
      'role': role.value,
      'status': status.value,
      'institutionId': institutionId,
      'regionId': regionId,
      'provinceId': provinceId,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'profileImageUrl': profileImageUrl,
      'metadata': metadata,
    };

  @override
  String toString() => 'UserModel(id: $id, email: $email, fullName: $fullName, role: ${role.displayName})';

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UserModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

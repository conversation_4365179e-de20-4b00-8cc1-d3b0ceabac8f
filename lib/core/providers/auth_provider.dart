import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

import '../models/user_model.dart';
import '../constants/app_constants.dart';
import '../services/auth_service.dart';

// Auth State
class AuthState {

  const AuthState({
    this.user,
    this.isLoading = false,
    this.error,
    this.isAuthenticated = false,
  });
  final UserModel? user;
  final bool isLoading;
  final String? error;
  final bool isAuthenticated;

  AuthState copyWith({
    UserModel? user,
    bool? isLoading,
    String? error,
    bool? isAuthenticated,
  }) => AuthState(
      user: user ?? this.user,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      isAuthenticated: isAuthenticated ?? this.isAuthenticated,
    );
}

// Auth Provider
class AuthNotifier extends StateNotifier<AuthState> {

  AuthNotifier(this._authService, this._prefs) : super(const AuthState()) {
    _loadUserFromStorage();
  }
  final AuthService _authService;
  final SharedPreferences _prefs;

  // Load user from local storage on app start
  Future<void> _loadUserFromStorage() async {
    try {
      final userJson = _prefs.getString(AppConstants.userDataKey);
      final token = _prefs.getString(AppConstants.userTokenKey);
      
      if (userJson != null && token != null) {
        final userData = json.decode(userJson);
        final user = UserModel.fromJson(userData);
        
        // Verify token is still valid
        final isValid = await _authService.verifyToken(token);
        if (isValid) {
          state = state.copyWith(
            user: user,
            isAuthenticated: true,
          );
        } else {
          await logout();
        }
      }
    } catch (e) {
      // If there's an error loading user data, clear it
      await logout();
    }
  }

  // Login with email and password
  Future<bool> login(String email, String password) async {
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      final result = await _authService.login(email, password);
      
      if (result['success'] == true) {
        final user = UserModel.fromJson(result['user']);
        final token = result['token'];
        
        // Save to local storage
        await _prefs.setString(AppConstants.userDataKey, json.encode(user.toJson()));
        await _prefs.setString(AppConstants.userTokenKey, token);
        
        state = state.copyWith(
          user: user,
          isAuthenticated: true,
          isLoading: false,
        );
        
        return true;
      } else {
        state = state.copyWith(
          error: result['message'] ?? 'فشل في تسجيل الدخول',
          isLoading: false,
        );
        return false;
      }
    } catch (e) {
      state = state.copyWith(
        error: 'خطأ في الاتصال بالخادم',
        isLoading: false,
      );
      return false;
    }
  }

  // Logout
  Future<void> logout() async {
    try {
      // Call logout API if user is authenticated
      if (state.isAuthenticated) {
        await _authService.logout();
      }
    } catch (e) {
      // Continue with logout even if API call fails
    }
    
    // Clear local storage
    await _prefs.remove(AppConstants.userDataKey);
    await _prefs.remove(AppConstants.userTokenKey);
    
    state = const AuthState();
  }

  // Update user profile
  Future<bool> updateProfile(UserModel updatedUser) async {
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      final result = await _authService.updateProfile(updatedUser);
      
      if (result['success'] == true) {
        final user = UserModel.fromJson(result['user']);
        
        // Update local storage
        await _prefs.setString(AppConstants.userDataKey, json.encode(user.toJson()));
        
        state = state.copyWith(
          user: user,
          isLoading: false,
        );
        
        return true;
      } else {
        state = state.copyWith(
          error: result['message'] ?? 'فشل في تحديث الملف الشخصي',
          isLoading: false,
        );
        return false;
      }
    } catch (e) {
      state = state.copyWith(
        error: 'خطأ في الاتصال بالخادم',
        isLoading: false,
      );
      return false;
    }
  }

  // Change password
  Future<bool> changePassword(String currentPassword, String newPassword) async {
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      final result = await _authService.changePassword(currentPassword, newPassword);
      
      if (result['success'] == true) {
        state = state.copyWith(isLoading: false);
        return true;
      } else {
        state = state.copyWith(
          error: result['message'] ?? 'فشل في تغيير كلمة المرور',
          isLoading: false,
        );
        return false;
      }
    } catch (e) {
      state = state.copyWith(
        error: 'خطأ في الاتصال بالخادم',
        isLoading: false,
      );
      return false;
    }
  }

  // Clear error
  void clearError() {
    state = state.copyWith(error: null);
  }

  // Check if user has permission for specific action
  bool hasPermission(String permission) {
    final user = state.user;
    if (user == null) return false;
    
    switch (permission) {
      case 'manage_regions':
        return user.canManageRegions();
      case 'manage_provinces':
        return user.canManageProvinces();
      case 'manage_schools':
        return user.canManageSchools();
      case 'manage_users':
        return user.canManageUsers();
      default:
        return false;
    }
  }

  // Get institutions user can manage
  List<String> getManagedInstitutionIds() {
    final user = state.user;
    if (user == null) return [];
    
    var ids = <String>[];
    
    if (user.institutionId != null) {
      ids.add(user.institutionId!);
    }
    
    // Add logic to get child institutions based on user role
    // This would typically come from the backend
    
    return ids;
  }
}

// Providers
final sharedPreferencesProvider = Provider<SharedPreferences>((ref) {
  throw UnimplementedError('SharedPreferences must be overridden');
});

final authServiceProvider = Provider<AuthService>((ref) => AuthService());

final authProvider = StateNotifierProvider<AuthNotifier, AuthState>((ref) {
  final authService = ref.watch(authServiceProvider);
  final prefs = ref.watch(sharedPreferencesProvider);
  return AuthNotifier(authService, prefs);
});

// Convenience providers
final currentUserProvider = Provider<UserModel?>((ref) => ref.watch(authProvider).user);

final isAuthenticatedProvider = Provider<bool>((ref) => ref.watch(authProvider).isAuthenticated);

final userRoleProvider = Provider<UserRole?>((ref) => ref.watch(authProvider).user?.role);

// Permission providers
final canManageRegionsProvider = Provider<bool>((ref) => ref.watch(authProvider.notifier).hasPermission('manage_regions'));

final canManageProvincesProvider = Provider<bool>((ref) => ref.watch(authProvider.notifier).hasPermission('manage_provinces'));

final canManageSchoolsProvider = Provider<bool>((ref) => ref.watch(authProvider.notifier).hasPermission('manage_schools'));

final canManageUsersProvider = Provider<bool>((ref) => ref.watch(authProvider.notifier).hasPermission('manage_users'));

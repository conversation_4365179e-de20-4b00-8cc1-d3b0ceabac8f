import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../features/auth/presentation/pages/login_page.dart';
import '../../features/dashboard/presentation/pages/dashboard_page.dart';
import '../../features/institutions/presentation/pages/institutions_page.dart';
import '../../features/users/presentation/pages/users_page.dart';
import '../../features/profile/presentation/pages/profile_page.dart';
import '../providers/auth_provider.dart';

// Route names
class AppRoutes {
  static const String login = '/login';
  static const String dashboard = '/dashboard';
  static const String institutions = '/institutions';
  static const String users = '/users';
  static const String profile = '/profile';
  static const String institutionDetails = '/institutions/:id';
  static const String userDetails = '/users/:id';
}

final routerProvider = Provider<GoRouter>((ref) {
  final authState = ref.watch(authProvider);
  
  return GoRouter(
    initialLocation: authState.isAuthenticated ? AppRoutes.dashboard : AppRoutes.login,
    redirect: (context, state) {
      final isAuthenticated = authState.isAuthenticated;
      final isLoggingIn = state.location == AppRoutes.login;
      
      // If not authenticated and not on login page, redirect to login
      if (!isAuthenticated && !isLoggingIn) {
        return AppRoutes.login;
      }
      
      // If authenticated and on login page, redirect to dashboard
      if (isAuthenticated && isLoggingIn) {
        return AppRoutes.dashboard;
      }
      
      return null; // No redirect needed
    },
    routes: [
      // Auth Routes
      GoRoute(
        path: AppRoutes.login,
        name: 'login',
        builder: (context, state) => const LoginPage(),
      ),
      
      // Main App Routes
      ShellRoute(
        builder: (context, state, child) => MainLayout(child: child),
        routes: [
          GoRoute(
            path: AppRoutes.dashboard,
            name: 'dashboard',
            builder: (context, state) => const DashboardPage(),
          ),
          GoRoute(
            path: AppRoutes.institutions,
            name: 'institutions',
            builder: (context, state) => const InstitutionsPage(),
            routes: [
              GoRoute(
                path: '/:id',
                name: 'institution-details',
                builder: (context, state) {
                  final id = state.pathParameters['id'];
                  return InstitutionDetailsPage(institutionId: id);
                },
              ),
            ],
          ),
          GoRoute(
            path: AppRoutes.users,
            name: 'users',
            builder: (context, state) => const UsersPage(),
            routes: [
              GoRoute(
                path: '/:id',
                name: 'user-details',
                builder: (context, state) {
                  final id = state.pathParameters['id'];
                  return UserDetailsPage(userId: id);
                },
              ),
            ],
          ),
          GoRoute(
            path: AppRoutes.profile,
            name: 'profile',
            builder: (context, state) => const ProfilePage(),
          ),
        ],
      ),
    ],
    errorBuilder: (context, state) => Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              'خطأ في التنقل',
              style: Theme.of(context).textTheme.headlineMedium,
            ),
            const SizedBox(height: 8),
            Text(
              'الصفحة المطلوبة غير موجودة',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => context.go(AppRoutes.dashboard),
              child: const Text('العودة للرئيسية'),
            ),
          ],
        ),
      ),
    ),
  );
});

// Main Layout Widget
class MainLayout extends ConsumerWidget {
  
  const MainLayout({
    super.key,
    required this.child,
  });
  final Widget child;

  @override
  Widget build(BuildContext context, WidgetRef ref) => Scaffold(
      body: Row(
        children: [
          // Sidebar Navigation
          const NavigationSidebar(),
          
          // Main Content
          Expanded(
            child: child,
          ),
        ],
      ),
    );
}

// Navigation Sidebar
class NavigationSidebar extends ConsumerWidget {
  const NavigationSidebar({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentLocation = GoRouterState.of(context).location;
    final user = ref.watch(authProvider).user;
    
    return Container(
      width: 280,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border(
          right: BorderSide(
            color: Theme.of(context).dividerColor,
            width: 1,
          ),
        ),
      ),
      child: Column(
        children: [
          // App Header
          Container(
            padding: const EdgeInsets.all(24),
            child: Column(
              children: [
                Text(
                  'إقرأ',
                  style: Theme.of(context).textTheme.displaySmall?.copyWith(
                    color: Theme.of(context).colorScheme.primary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'نظام إدارة المدارس',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                  ),
                ),
              ],
            ),
          ),
          
          const Divider(),
          
          // Navigation Items
          Expanded(
            child: ListView(
              padding: const EdgeInsets.all(16),
              children: [
                _NavigationItem(
                  icon: Icons.dashboard,
                  title: 'لوحة التحكم',
                  route: AppRoutes.dashboard,
                  isSelected: currentLocation == AppRoutes.dashboard,
                ),
                if (user?.canManageSchools() ?? false)
                  _NavigationItem(
                    icon: Icons.school,
                    title: 'المؤسسات التعليمية',
                    route: AppRoutes.institutions,
                    isSelected: currentLocation.startsWith('/institutions'),
                  ),
                if (user?.canManageUsers() ?? false)
                  _NavigationItem(
                    icon: Icons.people,
                    title: 'المستخدمون',
                    route: AppRoutes.users,
                    isSelected: currentLocation.startsWith('/users'),
                  ),
                _NavigationItem(
                  icon: Icons.person,
                  title: 'الملف الشخصي',
                  route: AppRoutes.profile,
                  isSelected: currentLocation == AppRoutes.profile,
                ),
              ],
            ),
          ),
          
          const Divider(),
          
          // User Info & Logout
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                if (user != null) ...[
                  ListTile(
                    leading: CircleAvatar(
                      child: Text(user.firstName[0]),
                    ),
                    title: Text(user.fullName),
                    subtitle: Text(user.role.displayName),
                  ),
                  const SizedBox(height: 8),
                ],
                SizedBox(
                  width: double.infinity,
                  child: OutlinedButton.icon(
                    onPressed: () => ref.read(authProvider.notifier).logout(),
                    icon: const Icon(Icons.logout),
                    label: const Text('تسجيل الخروج'),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class _NavigationItem extends StatelessWidget {

  const _NavigationItem({
    required this.icon,
    required this.title,
    required this.route,
    required this.isSelected,
  });
  final IconData icon;
  final String title;
  final String route;
  final bool isSelected;

  @override
  Widget build(BuildContext context) => Container(
      margin: const EdgeInsets.only(bottom: 4),
      child: ListTile(
        leading: Icon(
          icon,
          color: isSelected 
              ? Theme.of(context).colorScheme.primary
              : Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
        ),
        title: Text(
          title,
          style: TextStyle(
            color: isSelected 
                ? Theme.of(context).colorScheme.primary
                : Theme.of(context).colorScheme.onSurface,
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
          ),
        ),
        selected: isSelected,
        selectedTileColor: Theme.of(context).colorScheme.primary.withOpacity(0.1),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        onTap: () => context.go(route),
      ),
    );
}

// Placeholder pages (will be implemented later)
class InstitutionDetailsPage extends StatelessWidget {
  
  const InstitutionDetailsPage({super.key, required this.institutionId});
  final String institutionId;
  
  @override
  Widget build(BuildContext context) => Scaffold(
      appBar: AppBar(title: const Text('تفاصيل المؤسسة')),
      body: Center(child: Text('Institution ID: $institutionId')),
    );
}

class UserDetailsPage extends StatelessWidget {
  
  const UserDetailsPage({super.key, required this.userId});
  final String userId;
  
  @override
  Widget build(BuildContext context) => Scaffold(
      appBar: AppBar(title: const Text('تفاصيل المستخدم')),
      body: Center(child: Text('User ID: $userId')),
    );
}

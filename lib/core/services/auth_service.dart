import 'package:http/http.dart' as http;
import '../constants/app_constants.dart';
import '../models/user_model.dart';

class AuthService {
  final String baseUrl = AppConstants.baseUrl;
  
  // Mock data for development - replace with real API calls
  final Map<String, Map<String, dynamic>> _mockUsers = {
    '<EMAIL>': {
      'password': 'admin123',
      'user': {
        'id': '1',
        'email': '<EMAIL>',
        'firstName': 'محمد',
        'lastName': 'الإدريسي',
        'phoneNumber': '+212600000001',
        'role': 'national_admin',
        'status': 'active',
        'institutionId': null,
        'regionId': null,
        'provinceId': null,
        'createdAt': '2024-01-01T00:00:00.000Z',
        'updatedAt': '2024-01-01T00:00:00.000Z',
        'profileImageUrl': null,
        'metadata': null,
      }
    },
    '<EMAIL>': {
      'password': 'regional123',
      'user': {
        'id': '2',
        'email': '<EMAIL>',
        'firstName': 'فاطمة',
        'lastName': 'الزهراء',
        'phoneNumber': '+212600000002',
        'role': 'regional_manager',
        'status': 'active',
        'institutionId': 'academy_1',
        'regionId': 'casablanca_settat',
        'provinceId': null,
        'createdAt': '2024-01-01T00:00:00.000Z',
        'updatedAt': '2024-01-01T00:00:00.000Z',
        'profileImageUrl': null,
        'metadata': null,
      }
    },
    '<EMAIL>': {
      'password': 'provincial123',
      'user': {
        'id': '3',
        'email': '<EMAIL>',
        'firstName': 'أحمد',
        'lastName': 'بنعلي',
        'phoneNumber': '+212600000003',
        'role': 'provincial_director',
        'status': 'active',
        'institutionId': 'direction_1',
        'regionId': 'casablanca_settat',
        'provinceId': 'casablanca',
        'createdAt': '2024-01-01T00:00:00.000Z',
        'updatedAt': '2024-01-01T00:00:00.000Z',
        'profileImageUrl': null,
        'metadata': null,
      }
    },
    '<EMAIL>': {
      'password': 'principal123',
      'user': {
        'id': '4',
        'email': '<EMAIL>',
        'firstName': 'خديجة',
        'lastName': 'المرابط',
        'phoneNumber': '+212600000004',
        'role': 'school_principal',
        'status': 'active',
        'institutionId': 'school_1',
        'regionId': 'casablanca_settat',
        'provinceId': 'casablanca',
        'createdAt': '2024-01-01T00:00:00.000Z',
        'updatedAt': '2024-01-01T00:00:00.000Z',
        'profileImageUrl': null,
        'metadata': null,
      }
    },
  };

  // Login
  Future<Map<String, dynamic>> login(String email, String password) async {
    // Simulate network delay
    await Future.delayed(const Duration(seconds: 1));
    
    // Mock authentication
    if (_mockUsers.containsKey(email)) {
      final userData = _mockUsers[email]!;
      if (userData['password'] == password) {
        return {
          'success': true,
          'user': userData['user'],
          'token': 'mock_token_${DateTime.now().millisecondsSinceEpoch}',
          'message': 'تم تسجيل الدخول بنجاح',
        };
      }
    }
    
    return {
      'success': false,
      'message': 'البريد الإلكتروني أو كلمة المرور غير صحيحة',
    };
    
    // Real API implementation would look like this:
    /*
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/auth/login'),
        headers: {
          'Content-Type': 'application/json',
        },
        body: json.encode({
          'email': email,
          'password': password,
        }),
      );
      
      final data = json.decode(response.body);
      
      if (response.statusCode == 200) {
        return {
          'success': true,
          'user': data['user'],
          'token': data['token'],
          'message': data['message'],
        };
      } else {
        return {
          'success': false,
          'message': data['message'] ?? 'خطأ في تسجيل الدخول',
        };
      }
    } catch (e) {
      return {
        'success': false,
        'message': 'خطأ في الاتصال بالخادم',
      };
    }
    */
  }

  // Logout
  Future<Map<String, dynamic>> logout() async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 500));
    
    return {
      'success': true,
      'message': 'تم تسجيل الخروج بنجاح',
    };
    
    // Real API implementation:
    /*
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/auth/logout'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );
      
      return {
        'success': response.statusCode == 200,
        'message': 'تم تسجيل الخروج بنجاح',
      };
    } catch (e) {
      return {
        'success': false,
        'message': 'خطأ في تسجيل الخروج',
      };
    }
    */
  }

  // Verify token
  Future<bool> verifyToken(String token) async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 300));
    
    // Mock verification - in real app, verify with backend
    return token.startsWith('mock_token_');
    
    // Real API implementation:
    /*
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/auth/verify'),
        headers: {
          'Authorization': 'Bearer $token',
        },
      );
      
      return response.statusCode == 200;
    } catch (e) {
      return false;
    }
    */
  }

  // Update profile
  Future<Map<String, dynamic>> updateProfile(UserModel user) async {
    // Simulate network delay
    await Future.delayed(const Duration(seconds: 1));
    
    // Mock update
    return {
      'success': true,
      'user': user.copyWith(updatedAt: DateTime.now()).toJson(),
      'message': 'تم تحديث الملف الشخصي بنجاح',
    };
    
    // Real API implementation:
    /*
    try {
      final response = await http.put(
        Uri.parse('$baseUrl/auth/profile'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        body: json.encode(user.toJson()),
      );
      
      final data = json.decode(response.body);
      
      if (response.statusCode == 200) {
        return {
          'success': true,
          'user': data['user'],
          'message': data['message'],
        };
      } else {
        return {
          'success': false,
          'message': data['message'] ?? 'فشل في تحديث الملف الشخصي',
        };
      }
    } catch (e) {
      return {
        'success': false,
        'message': 'خطأ في الاتصال بالخادم',
      };
    }
    */
  }

  // Change password
  Future<Map<String, dynamic>> changePassword(String currentPassword, String newPassword) async {
    // Simulate network delay
    await Future.delayed(const Duration(seconds: 1));
    
    // Mock password change
    return {
      'success': true,
      'message': 'تم تغيير كلمة المرور بنجاح',
    };
    
    // Real API implementation:
    /*
    try {
      final response = await http.put(
        Uri.parse('$baseUrl/auth/change-password'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        body: json.encode({
          'currentPassword': currentPassword,
          'newPassword': newPassword,
        }),
      );
      
      final data = json.decode(response.body);
      
      if (response.statusCode == 200) {
        return {
          'success': true,
          'message': data['message'],
        };
      } else {
        return {
          'success': false,
          'message': data['message'] ?? 'فشل في تغيير كلمة المرور',
        };
      }
    } catch (e) {
      return {
        'success': false,
        'message': 'خطأ في الاتصال بالخادم',
      };
    }
    */
  }

  // Get current user
  Future<Map<String, dynamic>> getCurrentUser(String token) async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 500));
    
    // Mock get current user
    return {
      'success': true,
      'user': _mockUsers.values.first['user'],
    };
    
    // Real API implementation:
    /*
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/auth/me'),
        headers: {
          'Authorization': 'Bearer $token',
        },
      );
      
      final data = json.decode(response.body);
      
      if (response.statusCode == 200) {
        return {
          'success': true,
          'user': data['user'],
        };
      } else {
        return {
          'success': false,
          'message': data['message'] ?? 'فشل في جلب بيانات المستخدم',
        };
      }
    } catch (e) {
      return {
        'success': false,
        'message': 'خطأ في الاتصال بالخادم',
      };
    }
    */
  }
}

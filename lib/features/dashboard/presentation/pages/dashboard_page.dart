import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';

import '../../../../core/providers/auth_provider.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/models/user_model.dart';

class DashboardPage extends ConsumerWidget {
  const DashboardPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final user = ref.watch(currentUserProvider);
    
    if (user == null) {
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('لوحة التحكم'),
        automaticallyImplyLeading: false,
        actions: [
          IconButton(
            icon: const Icon(Icons.notifications),
            onPressed: () {
              // TODO: Implement notifications
            },
          ),
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () {
              // TODO: Implement settings
            },
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Welcome Section
            _buildWelcomeSection(context, user),
            
            const SizedBox(height: AppConstants.largePadding),
            
            // Quick Stats
            _buildQuickStats(context, user),
            
            const SizedBox(height: AppConstants.largePadding),
            
            // Quick Actions
            _buildQuickActions(context, user),
            
            const SizedBox(height: AppConstants.largePadding),
            
            // Recent Activity (placeholder)
            _buildRecentActivity(context),
          ],
        ),
      ),
    );
  }

  Widget _buildWelcomeSection(BuildContext context, UserModel user) {
    return Card(
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(AppConstants.largePadding),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              Theme.of(context).colorScheme.primary,
              Theme.of(context).colorScheme.primary.withOpacity(0.8),
            ],
          ),
          borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                CircleAvatar(
                  radius: 30,
                  backgroundColor: Colors.white.withOpacity(0.2),
                  child: Text(
                    user.firstName[0],
                    style: const TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: AppConstants.defaultPadding),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'مرحباً، ${user.fullName}',
                        style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        user.role.displayName,
                        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                          color: Colors.white.withOpacity(0.9),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            Text(
              _getWelcomeMessage(user.role),
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.white.withOpacity(0.9),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickStats(BuildContext context, UserModel user) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'إحصائيات سريعة',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: AppConstants.defaultPadding),
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: _getGridCrossAxisCount(context),
          crossAxisSpacing: AppConstants.defaultPadding,
          mainAxisSpacing: AppConstants.defaultPadding,
          childAspectRatio: 1.5,
          children: _getStatsCards(context, user),
        ),
      ],
    );
  }

  Widget _buildQuickActions(BuildContext context, UserModel user) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'إجراءات سريعة',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: AppConstants.defaultPadding),
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: _getGridCrossAxisCount(context),
          crossAxisSpacing: AppConstants.defaultPadding,
          mainAxisSpacing: AppConstants.defaultPadding,
          childAspectRatio: 1.2,
          children: _getActionCards(context, user),
        ),
      ],
    );
  }

  Widget _buildRecentActivity(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'النشاط الأخير',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: AppConstants.defaultPadding),
        Card(
          child: Padding(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            child: Column(
              children: [
                _buildActivityItem(
                  context,
                  'تم إضافة مدرسة جديدة',
                  'مدرسة الأمل الابتدائية',
                  Icons.school,
                  '2 ساعات',
                ),
                const Divider(),
                _buildActivityItem(
                  context,
                  'تم تحديث بيانات مستخدم',
                  'أحمد محمد - مدير مدرسة',
                  Icons.person,
                  '4 ساعات',
                ),
                const Divider(),
                _buildActivityItem(
                  context,
                  'تم إنشاء تقرير جديد',
                  'تقرير الإحصائيات الشهرية',
                  Icons.assessment,
                  '1 يوم',
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildActivityItem(
    BuildContext context,
    String title,
    String subtitle,
    IconData icon,
    String time,
  ) {
    return ListTile(
      leading: CircleAvatar(
        backgroundColor: Theme.of(context).colorScheme.primary.withOpacity(0.1),
        child: Icon(
          icon,
          color: Theme.of(context).colorScheme.primary,
        ),
      ),
      title: Text(title),
      subtitle: Text(subtitle),
      trailing: Text(
        time,
        style: Theme.of(context).textTheme.bodySmall?.copyWith(
          color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
        ),
      ),
    );
  }

  List<Widget> _getStatsCards(BuildContext context, UserModel user) {
    final List<Map<String, dynamic>> stats = [];
    
    switch (user.role) {
      case UserRole.nationalAdmin:
        stats.addAll([
          {'title': 'الأكاديميات الجهوية', 'value': '12', 'icon': MdiIcons.cityVariant, 'color': Colors.blue},
          {'title': 'النيابات الإقليمية', 'value': '75', 'icon': MdiIcons.office, 'color': Colors.green},
          {'title': 'المدارس', 'value': '8,542', 'icon': Icons.school, 'color': Colors.orange},
          {'title': 'المستخدمون', 'value': '12,345', 'icon': Icons.people, 'color': Colors.purple},
        ]);
        break;
      case UserRole.regionalManager:
        stats.addAll([
          {'title': 'النيابات الإقليمية', 'value': '6', 'icon': MdiIcons.office, 'color': Colors.green},
          {'title': 'المدارس', 'value': '1,234', 'icon': Icons.school, 'color': Colors.orange},
          {'title': 'الطلاب', 'value': '45,678', 'icon': Icons.person, 'color': Colors.blue},
          {'title': 'المعلمون', 'value': '2,345', 'icon': MdiIcons.teach, 'color': Colors.purple},
        ]);
        break;
      case UserRole.provincialDirector:
        stats.addAll([
          {'title': 'المدارس', 'value': '156', 'icon': Icons.school, 'color': Colors.orange},
          {'title': 'الطلاب', 'value': '8,234', 'icon': Icons.person, 'color': Colors.blue},
          {'title': 'المعلمون', 'value': '456', 'icon': MdiIcons.teach, 'color': Colors.purple},
          {'title': 'الموظفون', 'value': '89', 'icon': Icons.badge, 'color': Colors.green},
        ]);
        break;
      case UserRole.schoolPrincipal:
        stats.addAll([
          {'title': 'الطلاب', 'value': '342', 'icon': Icons.person, 'color': Colors.blue},
          {'title': 'المعلمون', 'value': '28', 'icon': MdiIcons.teach, 'color': Colors.purple},
          {'title': 'الفصول', 'value': '16', 'icon': MdiIcons.door, 'color': Colors.orange},
          {'title': 'الموظفون', 'value': '12', 'icon': Icons.badge, 'color': Colors.green},
        ]);
        break;
    }
    
    return stats.map((stat) => _buildStatCard(
      context,
      stat['title'],
      stat['value'],
      stat['icon'],
      stat['color'],
    )).toList();
  }

  List<Widget> _getActionCards(BuildContext context, UserModel user) {
    final List<Map<String, dynamic>> actions = [];
    
    if (user.canManageSchools()) {
      actions.add({
        'title': 'إدارة المؤسسات',
        'icon': Icons.school,
        'color': Colors.blue,
        'onTap': () {
          // TODO: Navigate to institutions
        },
      });
    }
    
    if (user.canManageUsers()) {
      actions.add({
        'title': 'إدارة المستخدمين',
        'icon': Icons.people,
        'color': Colors.green,
        'onTap': () {
          // TODO: Navigate to users
        },
      });
    }
    
    actions.addAll([
      {
        'title': 'التقارير',
        'icon': Icons.assessment,
        'color': Colors.orange,
        'onTap': () {
          // TODO: Navigate to reports
        },
      },
      {
        'title': 'الإعدادات',
        'icon': Icons.settings,
        'color': Colors.purple,
        'onTap': () {
          // TODO: Navigate to settings
        },
      },
    ]);
    
    return actions.map((action) => _buildActionCard(
      context,
      action['title'],
      action['icon'],
      action['color'],
      action['onTap'],
    )).toList();
  }

  Widget _buildStatCard(
    BuildContext context,
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: 32,
              color: color,
            ),
            const SizedBox(height: 8),
            Text(
              value,
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: Theme.of(context).textTheme.bodySmall,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionCard(
    BuildContext context,
    String title,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return Card(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                size: 32,
                color: color,
              ),
              const SizedBox(height: 8),
              Text(
                title,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _getWelcomeMessage(UserRole role) {
    switch (role) {
      case UserRole.nationalAdmin:
        return 'لديك صلاحية كاملة لإدارة جميع المؤسسات التعليمية في المملكة المغربية';
      case UserRole.regionalManager:
        return 'يمكنك إدارة النيابات الإقليمية والمدارس في أكاديميتك الجهوية';
      case UserRole.provincialDirector:
        return 'يمكنك إدارة المدارس والمؤسسات التعليمية في نيابتك الإقليمية';
      case UserRole.schoolPrincipal:
        return 'يمكنك إدارة مدرستك ومتابعة جميع الأنشطة التعليمية';
    }
  }

  int _getGridCrossAxisCount(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    if (width > 1200) return 4;
    if (width > 800) return 3;
    if (width > 600) return 2;
    return 2;
  }
}
